<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Task Management App</title>
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>

<body>
  <div class="app-container">
    <!-- Sidebar -->
    <div class="sidebar">
      <div class="sidebar-header">
        <h2>Menu</h2>
        <button class="theme-toggle" id="themeToggle">
          <i class="fas fa-moon"></i>
        </button>
      </div>

      <div class="search-container">
        <i class="fas fa-search"></i>
        <input type="text" placeholder="Search">
      </div>

      <div class="sidebar-section">
        <h3>TASKS</h3>
        <ul class="sidebar-menu">
          <li><i class="fas fa-angle-right"></i> Upcoming <span class="count">12</span></li>
          <li class="active"><i class="fas fa-calendar-day"></i> Today <span class="count">5</span></li>
          <li><i class="fas fa-calendar"></i> Calendar</li>
          <li><i class="fas fa-sticky-note"></i> Sticky Wall</li>
        </ul>
      </div>

      <div class="sidebar-section">
        <h3>LISTS</h3>
        <ul class="sidebar-menu">
          <li><i class="fas fa-circle" style="color: #ff6b6b;"></i> Personal <span class="count">3</span></li>
          <li><i class="fas fa-circle" style="color: #4ecdc4;"></i> Work <span class="count">6</span></li>
          <li><i class="fas fa-circle" style="color: #ffd166;"></i> List 1 <span class="count">3</span></li>
          <li><i class="fas fa-plus"></i> Add New List</li>
        </ul>
      </div>

      <div class="sidebar-section">
        <h3>TAGS</h3>
        <div class="tags-container">
          <span class="tag">Tag 1</span>
          <span class="tag">Tag 2</span>
          <span class="tag add-tag">+ Add Tag</span>
        </div>
      </div>

      <div class="sidebar-footer">
        <ul class="sidebar-menu">
          <li><i class="fas fa-cog"></i> Settings</li>
          <li><i class="fas fa-sign-out-alt"></i> Sign out</li>
        </ul>
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <div class="main-header">
        <h1>Today <span class="task-count">5</span></h1>
      </div>

      <div class="add-task">
        <i class="fas fa-plus"></i>
        <span>Add New Task</span>
      </div>

      <div class="task-list">
        <div class="task-item">
          <input type="checkbox" id="task1">
          <label for="task1">Research content ideas</label>
          <i class="fas fa-chevron-right"></i>
        </div>

        <div class="task-item">
          <input type="checkbox" id="task2">
          <label for="task2">Create a database of guest authors</label>
          <i class="fas fa-chevron-right"></i>
        </div>

        <div class="task-item selected">
          <input type="checkbox" id="task3">
          <label for="task3">Renew driver's license</label>
          <i class="fas fa-chevron-right"></i>
        </div>

        <div class="task-item">
          <input type="checkbox" id="task4">
          <label for="task4">Consult accountant</label>
          <i class="fas fa-chevron-right"></i>
        </div>

        <div class="task-item">
          <input type="checkbox" id="task5">
          <label for="task5">Print business card</label>
          <i class="fas fa-chevron-right"></i>
        </div>
      </div>
    </div>

    <!-- Task Details Panel -->
    <div class="task-details">
      <div class="task-details-header">
        <h2>Task:</h2>
      </div>

      <div class="task-details-content">
        <h3>Renew driver's license</h3>

        <div class="detail-section">
          <h4>Description</h4>
          <div class="description-content"></div>
        </div>

        <div class="detail-section">
          <div class="detail-row">
            <span>List</span>
            <div class="detail-value">
              <span class="list-badge personal">Personal</span>
              <i class="fas fa-chevron-down"></i>
            </div>
          </div>

          <div class="detail-row">
            <span>Due date</span>
            <div class="detail-value">
              <span>11-05-22</span>
              <i class="fas fa-chevron-down"></i>
            </div>
          </div>

          <div class="detail-row">
            <span>Tags</span>
            <div class="detail-value tags-list">
              <span class="tag">Tag 1</span>
              <span class="add-tag">+ Add Tag</span>
            </div>
          </div>
        </div>

        <div class="subtasks-section">
          <h4>Subtasks:</h4>
          <div class="add-subtask">
            <i class="fas fa-plus"></i>
            <span>Add New Subtask</span>
          </div>

          <div class="subtask-item">
            <input type="checkbox" id="subtask1">
            <label for="subtask1">Subtask</label>
          </div>
        </div>
      </div>

      <div class="task-details-footer">
        <button class="btn delete-btn">Delete Task</button>
        <button class="btn save-btn">Save changes</button>
      </div>
    </div>
  </div>

  <!-- Task Creation/Edit Modal -->
  <div class="modal" id="taskModal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 id="modalTitle">Add New Task</h2>
        <span class="close-modal">&times;</span>
      </div>
      <div class="modal-body">
        <form id="taskForm">
          <div class="form-group">
            <label for="taskTitle">Task Title</label>
            <input type="text" id="taskTitle" placeholder="Enter task title" required>
          </div>

          <div class="form-group">
            <label for="taskDescription">Description</label>
            <textarea id="taskDescription" placeholder="Enter task description"></textarea>
          </div>

          <div class="form-group">
            <label for="taskList">List</label>
            <select id="taskList">
              <option value="personal">Personal</option>
              <option value="work">Work</option>
              <option value="list1">List 1</option>
            </select>
          </div>

          <div class="form-group">
            <label for="taskDueDate">Due Date</label>
            <input type="date" id="taskDueDate">
          </div>

          <div class="form-group">
            <label for="taskTags">Tags</label>
            <div class="tags-input-container">
              <input type="text" id="taskTags" placeholder="Add a tag and press Enter">
              <div class="selected-tags" id="selectedTags"></div>
            </div>
          </div>

          <input type="hidden" id="taskId">
          <div class="form-actions">
            <button type="button" class="btn cancel-btn">Cancel</button>
            <button type="submit" class="btn save-btn">Save Task</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Calendar Modal -->
  <div class="modal" id="calendarModal">
    <div class="modal-content calendar-modal-content">
      <div class="modal-header">
        <h2>Calendar</h2>
        <span class="close-modal">&times;</span>
      </div>
      <div class="modal-body">
        <div class="calendar-container">
          <div class="calendar-header">
            <button id="prevMonth"><i class="fas fa-chevron-left"></i></button>
            <h3 id="currentMonth">September 2023</h3>
            <button id="nextMonth"><i class="fas fa-chevron-right"></i></button>
          </div>
          <div class="weekdays">
            <div>Sun</div>
            <div>Mon</div>
            <div>Tue</div>
            <div>Wed</div>
            <div>Thu</div>
            <div>Fri</div>
            <div>Sat</div>
          </div>
          <div class="calendar-days" id="calendarDays">
            <!-- Calendar days will be generated by JavaScript -->
          </div>
        </div>

        <div class="calendar-actions">
          <button id="connectGoogleBtn" class="btn google-btn">
            <i class="fab fa-google"></i> Connect Google Calendar
          </button>
        </div>

        <div class="day-events" id="dayEvents">
          <h3>Events for <span id="selectedDate">Today</span></h3>
          <div class="events-list" id="eventsList">
            <!-- Events will be displayed here -->
          </div>
          <button id="addEventBtn" class="btn add-event-btn">
            <i class="fas fa-plus"></i> Add Event
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Event Creation Modal -->
  <div class="modal" id="eventModal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 id="eventModalTitle">Add New Event</h2>
        <span class="close-modal">&times;</span>
      </div>
      <div class="modal-body">
        <form id="eventForm">
          <div class="form-group">
            <label for="eventTitle">Event Title</label>
            <input type="text" id="eventTitle" placeholder="Enter event title" required>
          </div>

          <div class="form-group">
            <label for="eventDescription">Description</label>
            <textarea id="eventDescription" placeholder="Enter event description"></textarea>
          </div>

          <div class="form-group">
            <label for="eventDate">Date</label>
            <input type="date" id="eventDate" required>
          </div>

          <div class="form-group time-group">
            <div>
              <label for="eventStartTime">Start Time</label>
              <input type="time" id="eventStartTime">
            </div>
            <div>
              <label for="eventEndTime">End Time</label>
              <input type="time" id="eventEndTime">
            </div>
          </div>

          <div class="form-group">
            <label for="eventLocation">Location</label>
            <input type="text" id="eventLocation" placeholder="Enter location">
          </div>

          <div class="form-group">
            <label>Reminder</label>
            <select id="eventReminder">
              <option value="none">None</option>
              <option value="5">5 minutes before</option>
              <option value="15">15 minutes before</option>
              <option value="30">30 minutes before</option>
              <option value="60">1 hour before</option>
              <option value="1440">1 day before</option>
            </select>
          </div>

          <div class="form-group">
            <label>Sync with Google Calendar</label>
            <div class="toggle-switch">
              <input type="checkbox" id="syncWithGoogle">
              <label for="syncWithGoogle"></label>
            </div>
          </div>

          <input type="hidden" id="eventId">
          <div class="form-actions">
            <button type="button" class="btn cancel-btn">Cancel</button>
            <button type="submit" class="btn save-btn">Save Event</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Settings Modal -->
  <div class="modal" id="settingsModal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Settings</h2>
        <span class="close-modal">&times;</span>
      </div>
      <div class="modal-body">
        <div class="settings-section">
          <h3>Account</h3>
          <div class="setting-item">
            <span>Email</span>
            <span class="setting-value"><EMAIL></span>
          </div>
          <div class="setting-item">
            <span>Password</span>
            <button class="btn small-btn">Change</button>
          </div>
        </div>

        <div class="settings-section">
          <h3>Appearance</h3>
          <div class="setting-item">
            <span>Theme</span>
            <div class="theme-selector">
              <button class="theme-btn light-theme-btn active">Light</button>
              <button class="theme-btn dark-theme-btn">Dark</button>
            </div>
          </div>
        </div>

        <div class="settings-section">
          <h3>Notifications</h3>
          <div class="setting-item">
            <span>Email Notifications</span>
            <div class="toggle-switch">
              <input type="checkbox" id="emailNotifications" checked>
              <label for="emailNotifications"></label>
            </div>
          </div>
          <div class="setting-item">
            <span>Push Notifications</span>
            <div class="toggle-switch">
              <input type="checkbox" id="pushNotifications">
              <label for="pushNotifications"></label>
            </div>
          </div>
        </div>

        <div class="settings-section">
          <h3>Integrations</h3>
          <div class="setting-item">
            <span>Google Calendar</span>
            <button class="btn small-btn connected-btn">Connected</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://apis.google.com/js/api.js"></script>
  <script src="script.js"></script>
</body>

</html>