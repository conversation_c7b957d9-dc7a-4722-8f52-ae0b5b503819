:root {
  /* Light theme (default) */
  --bg-color: #e8f0e8;
  --card-bg: #ffffff;
  --sidebar-bg: #ffffff;
  --text-color: #333333;
  --text-secondary: #666666;
  --border-color: #e0e0e0;
  --accent-color: #2196f3;
  --accent-hover: #1976d2;
  --checkbox-bg: #f0f0f0;
  --tag-bg: #e9f5f9;
  --delete-btn: #f5f5f5;
  --delete-text: #666666;
  --save-btn: #ffd166;
  --save-text: #333333;
  --pro-badge: #2196f3;
}

[data-theme="dark"] {
  --bg-color: #1a1a1a;
  --card-bg: #2d2d2d;
  --sidebar-bg: #252525;
  --text-color: #f0f0f0;
  --text-secondary: #b0b0b0;
  --border-color: #444444;
  --accent-color: #64b5f6;
  --accent-hover: #42a5f5;
  --checkbox-bg: #3d3d3d;
  --tag-bg: #3a4a5a;
  --delete-btn: #3d3d3d;
  --delete-text: #e0e0e0;
  --save-btn: #d4ac16;
  --save-text: #f0f0f0;
  --pro-badge: #42a5f5;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

html,
body {
  height: 100%;
  overflow-x: hidden;
}

body {
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-container {
  display: flex;
  max-width: 1200px;
  height: 85vh;
  min-height: 600px;
  margin: 40px auto;
  border-radius: 20px;
  overflow: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  background-color: var(--card-bg);
}

/* Sidebar Styles */
.sidebar {
  width: 280px;
  background-color: var(--sidebar-bg);
  border-right: 1px solid var(--border-color);
  padding: 20px;
  display: flex;
  flex-direction: column;
  transition: background-color 0.3s ease;
  overflow-y: auto;
  max-height: 100%;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.theme-toggle {
  background: none;
  border: none;
  color: var(--text-color);
  font-size: 18px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.search-container {
  position: relative;
  margin-bottom: 25px;
}

.search-container input {
  width: 100%;
  padding: 10px 10px 10px 35px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-color);
  transition: all 0.3s ease;
}

.search-container i {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.sidebar-section {
  margin-bottom: 25px;
}

.sidebar-section h3 {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 10px;
  font-weight: 600;
}

.sidebar-menu {
  list-style: none;
}

.sidebar-menu li {
  padding: 8px 10px;
  border-radius: 8px;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.sidebar-menu li:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.sidebar-menu li.active {
  background-color: rgba(33, 150, 243, 0.1);
  font-weight: 500;
}

.sidebar-menu li i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.count {
  margin-left: auto;
  background-color: var(--checkbox-bg);
  color: var(--text-secondary);
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  background-color: var(--tag-bg);
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  cursor: pointer;
}

.add-tag {
  color: var(--accent-color);
}

.sidebar-footer {
  margin-top: auto;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
}

/* Main Content Styles */
.main-content {
  flex: 1;
  padding: 20px;
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
  max-height: 100%;
}

.main-header {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.main-header h1 {
  font-size: 28px;
  font-weight: 600;
}

.task-count {
  font-size: 20px;
  color: var(--text-secondary);
  margin-left: 10px;
}

.add-task {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  color: var(--accent-color);
  margin-bottom: 15px;
}

.add-task i {
  margin-right: 10px;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.task-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.task-item.selected {
  background-color: rgba(33, 150, 243, 0.1);
}

.task-item input[type="checkbox"] {
  margin-right: 15px;
  appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--checkbox-bg);
  cursor: pointer;
  position: relative;
}

.task-item input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--accent-color);
  font-size: 14px;
}

.task-item label {
  flex: 1;
}

.task-item i {
  color: var(--text-secondary);
}

/* Task Details Styles */
.task-details {
  width: 350px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  max-height: 100%;
}

.task-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pro-badge {
  background-color: var(--pro-badge);
  color: white;
  padding: 8px 25px;
  border-radius: 10px;
  font-weight: bold;
  font-size: 20px;
}

.task-details-content {
  flex: 1;
  overflow-y: auto;
}

.task-details-content h3 {
  margin-bottom: 20px;
  font-size: 20px;
}

.detail-section {
  margin-bottom: 25px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: var(--text-secondary);
  font-weight: normal;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.detail-value {
  display: flex;
  align-items: center;
}

.detail-value i {
  margin-left: 10px;
  color: var(--text-secondary);
}

.list-badge {
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 14px;
}

.list-badge.personal {
  background-color: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
}

.tags-list {
  display: flex;
  gap: 8px;
}

.subtasks-section h4 {
  margin-bottom: 15px;
}

.add-subtask {
  display: flex;
  align-items: center;
  color: var(--accent-color);
  margin-bottom: 15px;
  cursor: pointer;
}

.add-subtask i {
  margin-right: 10px;
}

.subtask-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.subtask-item input[type="checkbox"] {
  margin-right: 10px;
  appearance: none;
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--checkbox-bg);
  cursor: pointer;
  position: relative;
}

.subtask-item input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--accent-color);
  font-size: 12px;
}

.task-details-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.btn {
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.delete-btn {
  background-color: var(--delete-btn);
  color: var(--delete-text);
}

.save-btn {
  background-color: var(--save-btn);
  color: var(--save-text);
}

/* Responsive adjustments */
@media (max-width: 1000px) {
  .app-container {
    flex-direction: column;
    height: auto;
    margin: 20px;
    max-height: 100vh;
    overflow: auto;
  }

  .sidebar,
  .main-content,
  .task-details {
    width: 100%;
    min-height: 300px;
    overflow-y: auto;
  }

  .sidebar {
    height: auto;
    max-height: 100vh;
    overflow-y: auto;
  }

  .main-content {
    border-right: none;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
  }
}

@media (max-width: 600px) {
  .app-container {
    margin: 10px;
    height: auto;
    min-height: 100vh;
  }

  body {
    padding: 0;
    margin: 0;
  }

  .sidebar,
  .main-content,
  .task-details {
    min-height: 200px;
    padding: 15px;
  }

  .sidebar-section {
    margin-bottom: 15px;
  }

  .sidebar-footer {
    display: block;
    position: relative;
    bottom: 0;
    width: 100%;
  }
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  overflow-y: auto;
}

.modal-content {
  background-color: var(--card-bg);
  margin: 50px auto;
  width: 90%;
  max-width: 500px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  animation: modalFadeIn 0.3s;
}

.calendar-modal-content {
  max-width: 800px;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 20px;
}

.close-modal {
  font-size: 24px;
  cursor: pointer;
  color: var(--text-secondary);
}

.close-modal:hover {
  color: var(--text-color);
}

.modal-body {
  padding: 20px;
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="date"],
.form-group input[type="time"],
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-color);
  font-size: 14px;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.time-group {
  display: flex;
  gap: 15px;
}

.time-group>div {
  flex: 1;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 30px;
}

.cancel-btn {
  background-color: var(--delete-btn);
  color: var(--delete-text);
}

.small-btn {
  padding: 5px 10px;
  font-size: 12px;
}

.connected-btn {
  background-color: #4caf50;
  color: white;
}

/* Tags Input */
.tags-input-container {
  border: 1px solid var(--border-color);
  border-radius: 5px;
  padding: 5px;
  background-color: var(--card-bg);
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 5px;
}

.selected-tag {
  background-color: var(--tag-bg);
  padding: 3px 8px;
  border-radius: 15px;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.selected-tag .remove-tag {
  margin-left: 5px;
  cursor: pointer;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-switch label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.toggle-switch label:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

.toggle-switch input:checked+label {
  background-color: var(--accent-color);
}

.toggle-switch input:checked+label:before {
  transform: translateX(26px);
}

/* Settings Styles */
.settings-section {
  margin-bottom: 30px;
}

.settings-section h3 {
  font-size: 16px;
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 1px solid var(--border-color);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.setting-value {
  color: var(--text-secondary);
}

.theme-selector {
  display: flex;
  gap: 10px;
}

.theme-btn {
  padding: 5px 15px;
  border-radius: 20px;
  border: 1px solid var(--border-color);
  background: none;
  cursor: pointer;
}

.theme-btn.active {
  background-color: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

/* Calendar Styles */
.calendar-container {
  margin-bottom: 20px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.calendar-header button {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: var(--text-color);
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  font-weight: bold;
  margin-bottom: 10px;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
}

.calendar-day {
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  cursor: pointer;
  position: relative;
}

.calendar-day:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.calendar-day.today {
  background-color: var(--accent-color);
  color: white;
}

.calendar-day.selected {
  border: 2px solid var(--accent-color);
}

.calendar-day.has-events::after {
  content: '';
  position: absolute;
  bottom: 5px;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: var(--accent-color);
}

.calendar-day.other-month {
  color: var(--text-secondary);
  opacity: 0.5;
}

.day-events {
  margin-top: 20px;
}

.events-list {
  margin: 15px 0;
  max-height: 300px;
  overflow-y: auto;
}

.event-item {
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 10px;
  background-color: rgba(33, 150, 243, 0.1);
  border-left: 3px solid var(--accent-color);
}

.event-time {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 5px;
}

.event-title {
  font-weight: 500;
  margin-bottom: 5px;
}

.event-location {
  font-size: 12px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
}

.event-location i {
  margin-right: 5px;
}

.add-event-btn {
  width: 100%;
  background-color: var(--accent-color);
  color: white;
  border: none;
  padding: 10px;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.add-event-btn i {
  margin-right: 5px;
}

.google-btn {
  background-color: #4285F4;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: 10px;
}

.google-btn i {
  margin-right: 10px;
}