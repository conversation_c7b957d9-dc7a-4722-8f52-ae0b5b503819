document.addEventListener('DOMContentLoaded', function() {
  // Theme toggle functionality
  const themeToggle = document.getElementById('themeToggle');
  const themeIcon = themeToggle.querySelector('i');
  
  // Check for saved theme preference or use default
  const savedTheme = localStorage.getItem('theme') || 'light';
  setTheme(savedTheme);
  
  // Toggle theme when button is clicked
  themeToggle.addEventListener('click', function() {
    const currentTheme = document.body.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);
  });
  
  function setTheme(theme) {
    if (theme === 'dark') {
      document.body.setAttribute('data-theme', 'dark');
      themeIcon.classList.remove('fa-moon');
      themeIcon.classList.add('fa-sun');
    } else {
      document.body.removeAttribute('data-theme');
      themeIcon.classList.remove('fa-sun');
      themeIcon.classList.add('fa-moon');
    }
  }
  
  // Task selection functionality
  const taskItems = document.querySelectorAll('.task-item');
  
  taskItems.forEach(task => {
    task.addEventListener('click', function(e) {
      // Don't trigger selection if checkbox was clicked
      if (e.target.type === 'checkbox') return;
      
      // Remove selected class from all tasks
      taskItems.forEach(t => t.classList.remove('selected'));
      
      // Add selected class to clicked task
      this.classList.add('selected');
      
      // Update task details panel
      updateTaskDetails(this);
    });
  });
  
  function updateTaskDetails(taskElement) {
    const taskName = taskElement.querySelector('label').textContent;
    const taskDetailsTitle = document.querySelector('.task-details-content h3');
    
    // Update task title in details panel
    taskDetailsTitle.textContent = taskName;
  }
  
  // Checkbox functionality
  const checkboxes = document.querySelectorAll('input[type="checkbox"]');
  
  checkboxes.forEach(checkbox => {
    checkbox.addEventListener('change', function() {
      const label = this.nextElementSibling;
      
      if (this.checked) {
        label.style.textDecoration = 'line-through';
        label.style.color = 'var(--text-secondary)';
      } else {
        label.style.textDecoration = 'none';
        label.style.color = 'var(--text-color)';
      }
    });
  });
  
  // Add new task functionality
  const addTaskButton = document.querySelector('.add-task');
  const taskList = document.querySelector('.task-list');
  
  addTaskButton.addEventListener('click', function() {
    const taskId = 'task' + (document.querySelectorAll('.task-item').length + 1);
    
    const newTaskElement = document.createElement('div');
    newTaskElement.className = 'task-item';
    newTaskElement.innerHTML = `
      <input type="checkbox" id="${taskId}">
      <label for="${taskId}">New Task</label>
      <i class="fas fa-chevron-right"></i>
    `;
    
    taskList.appendChild(newTaskElement);
    
    // Add event listener to new task
    newTaskElement.addEventListener('click', function(e) {
      if (e.target.type === 'checkbox') return;
      
      taskItems.forEach(t => t.classList.remove('selected'));
      this.classList.add('selected');
      updateTaskDetails(this);
    });
    
    // Update task count
    updateTaskCount();
  });
  
  // Add new subtask functionality
  const addSubtaskButton = document.querySelector('.add-subtask');
  const subtasksList = document.querySelector('.subtasks-section');
  
  addSubtaskButton.addEventListener('click', function() {
    const subtaskId = 'subtask' + (document.querySelectorAll('.subtask-item').length + 1);
    
    const newSubtaskElement = document.createElement('div');
    newSubtaskElement.className = 'subtask-item';
    newSubtaskElement.innerHTML = `
      <input type="checkbox" id="${subtaskId}">
      <label for="${subtaskId}">New Subtask</label>
    `;
    
    subtasksList.appendChild(newSubtaskElement);
  });
  
  // Save changes button functionality
  const saveButton = document.querySelector('.save-btn');
  
  saveButton.addEventListener('click', function() {
    alert('Changes saved successfully!');
  });
  
  // Delete task button functionality
  const deleteButton = document.querySelector('.delete-btn');
  
  deleteButton.addEventListener('click', function() {
    const selectedTask = document.querySelector('.task-item.selected');
    
    if (selectedTask) {
      selectedTask.remove();
      updateTaskCount();
      
      // Select first task if available
      const firstTask = document.querySelector('.task-item');
      if (firstTask) {
        firstTask.classList.add('selected');
        updateTaskDetails(firstTask);
      } else {
        // Clear task details if no tasks left
        document.querySelector('.task-details-content h3').textContent = '';
      }
    }
  });
  
  function updateTaskCount() {
    const taskCount = document.querySelectorAll('.task-item').length;
    document.querySelector('.task-count').textContent = taskCount;
  }
});
